"use client";

import {
   <PERSON>bar,
   Sidebar<PERSON>ontent,
   SidebarFooter,
   SidebarGroup,
   SidebarGroupContent,
   SidebarHeader,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarTrigger,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import {
   Bars3BottomRightIcon,
   BookmarkSquareIcon,
   HomeIcon,
   PhotoIcon,
   TagIcon,
   UserIcon,
   WrenchScrewdriverIcon,
} from "@heroicons/react/24/solid";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navigationItems = [
   {
      name: "Home",
      href: "/admin",
      icon: HomeIcon,
      description: "Dashboard overview",
   },
   {
      name: "Albums",
      href: "/admin/albums",
      icon: BookmarkSquareIcon,
      description: "Album management",
   },
   {
      name: "Collections",
      href: "/admin/collections",
      icon: TagIcon,
      description: "Collection management",
   },
   {
      name: "Ungrouped",
      href: "/admin/ungrouped",
      icon: PhotoIcon,
      description: "Images not in albums",
   },
   {
      name: "Account",
      href: "/admin/account",
      icon: UserIcon,
      description: "Account settings",
   },
];

function AdminSidebarContent() {
   const pathname = usePathname();

   const isActive = (href: string) => {
      if (href === "/admin") {
         return pathname === "/admin";
      }
      return pathname.startsWith(href);
   };

   return (
      <>
         <SidebarHeader className="p-6 border-b border-border/50">
            <div className="flex items-center space-x-3">
               <div className="flex items-center">
                  {/* Logo */}
                  <Link href="/">
                     <Image
                        src="/astral-logo.svg"
                        alt="Astral logo"
                        width={100}
                        height={40}
                        className="h-10 w-auto"
                     />
                  </Link>
               </div>
            </div>
         </SidebarHeader>

         <SidebarContent className="flex-1 p-2">
            <SidebarGroup>
               <SidebarGroupContent>
                  <SidebarMenu className="space-y-2">
                     {navigationItems.map((item) => {
                        const Icon = item.icon;
                        const active = isActive(item.href);

                        return (
                           <SidebarMenuItem key={item.name}>
                              <SidebarMenuButton
                                 asChild
                                 isActive={active}
                                 className={cn(
                                    "h-auto p-0 hover:bg-transparent",
                                    active && "bg-transparent"
                                 )}
                              >
                                 <Link
                                    href={item.href}
                                    className={cn(
                                       "flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group/item w-full",
                                       active
                                          ? "bg-primary text-primary-foreground shadow-sm"
                                          : "text-muted-foreground hover:text-foreground hover:bg-accent"
                                    )}
                                 >
                                    <Icon
                                       className={cn(
                                          "size-5 shrink-0 transition-colors",
                                          active
                                             ? "text-primary-foreground"
                                             : "text-muted-foreground group-hover/item:text-foreground"
                                       )}
                                    />
                                    <div className="flex-1 min-w-0">
                                       <div
                                          className={cn(
                                             "text-sm font-semibold",
                                             active
                                                ? "text-primary-foreground"
                                                : ""
                                          )}
                                       >
                                          {item.name}
                                       </div>
                                       <div
                                          className={cn(
                                             "text-xs font-semibold truncate transition-colors",
                                             active
                                                ? "text-primary-foreground/80"
                                                : "text-muted-foreground/80 group-hover/item:text-foreground/80"
                                          )}
                                       >
                                          {item.description}
                                       </div>
                                    </div>
                                 </Link>
                              </SidebarMenuButton>
                           </SidebarMenuItem>
                        );
                     })}
                  </SidebarMenu>
               </SidebarGroupContent>
            </SidebarGroup>
         </SidebarContent>

         <SidebarFooter className="p-4 border-t border-border/50">
            <div className="flex items-center space-x-3 px-4 py-3">
               <div className="w-8 h-8 bg-astral-grey-light rounded-full flex items-center justify-center">
                  <WrenchScrewdriverIcon className="w-4 h-4 text-muted-foreground" />
               </div>
               <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-foreground">
                     Admin User
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                     <EMAIL>
                  </div>
               </div>
            </div>
         </SidebarFooter>
      </>
   );
}

export function AdminSidebar() {
   return (
      <Sidebar
         variant="sidebar"
         collapsible="offcanvas"
         className="w-64 bg-card border-r border-border/50"
      >
         <AdminSidebarContent />
      </Sidebar>
   );
}

// Mobile trigger component for the top header
export function AdminSidebarMobileTrigger() {
   return (
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-card border-b border-border/50 px-4 py-3">
         <div className="flex items-center justify-between">
            <div className="flex items-center">
               {/* Logo */}
               <Link href="/">
                  <Image
                     src="/astral-logo.svg"
                     alt="Astral logo"
                     width={100}
                     height={40}
                     className="h-10 w-auto"
                  />
               </Link>
            </div>
            <SidebarTrigger className="p-2">
               <Bars3BottomRightIcon className="size-6" />
               <span className="sr-only">Toggle menu</span>
            </SidebarTrigger>
         </div>
      </div>
   );
}
