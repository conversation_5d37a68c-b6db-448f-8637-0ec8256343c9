@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@utility container {
   @variant lg {
      max-width: 1440px;
   }
}

@theme inline {
   --color-background: var(--background);
   --color-foreground: var(--foreground);
   --font-sans: var(--font-geist-sans);
   --font-mono: var(--font-geist-mono);
   --color-sidebar-ring: var(--sidebar-ring);
   --color-sidebar-border: var(--sidebar-border);
   --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
   --color-sidebar-accent: var(--sidebar-accent);
   --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
   --color-sidebar-primary: var(--sidebar-primary);
   --color-sidebar-foreground: var(--sidebar-foreground);
   --color-sidebar: var(--sidebar);
   --color-chart-5: var(--chart-5);
   --color-chart-4: var(--chart-4);
   --color-chart-3: var(--chart-3);
   --color-chart-2: var(--chart-2);
   --color-chart-1: var(--chart-1);
   --color-ring: var(--ring);
   --color-input: var(--input);
   --color-border: var(--border);
   --color-destructive: var(--destructive);
   --color-accent-foreground: var(--accent-foreground);
   --color-accent: var(--accent);
   --color-muted-foreground: var(--muted-foreground);
   --color-muted: var(--muted);
   --color-secondary-foreground: var(--secondary-foreground);
   --color-secondary: var(--secondary);
   --color-primary-foreground: var(--primary-foreground);
   --color-primary: var(--primary);
   --color-popover-foreground: var(--popover-foreground);
   --color-popover: var(--popover);
   --color-card-foreground: var(--card-foreground);
   --color-card: var(--card);
   --radius-sm: calc(var(--radius) - 4px);
   --radius-md: calc(var(--radius) - 2px);
   --radius-lg: var(--radius);
   --radius-xl: calc(var(--radius) + 4px);

   /* Custom Astral Studios colors */
   --color-astral-red: hsl(0 84% 60%);
   --color-astral-red-light: hsl(0 84% 70%);
   --color-astral-red-dark: hsl(0 84% 45%);
   --color-astral-black: hsl(0 0% 2%);
   --color-astral-grey: hsl(0 0% 8%);
   --color-astral-grey-light: hsl(0 0% 15%);
}

:root {
   --radius: 0.625rem;
   --background: hsl(0 0% 2%);
   --foreground: hsl(0 0% 98%);
   --card: hsl(0 0% 5%);
   --card-foreground: hsl(0 0% 98%);
   --popover: hsl(0 0% 5%);
   --popover-foreground: hsl(0 0% 98%);
   --primary: oklch(0.55 0.22 25); /* Red primary color */
   --primary-foreground: oklch(1 0 0);
   --secondary: oklch(0.15 0 0); /* Black secondary color */
   --secondary-foreground: oklch(1 0 0);
   --muted: oklch(0.97 0 0);
   --muted-foreground: hsl(0 0% 65%);
   --accent: oklch(0.55 0.22 25); /* Red accent color */
   --accent-foreground: oklch(1 0 0);
   --destructive: oklch(0.577 0.245 27.325);
   --border: hsl(0 0% 15%);
   --input: hsl(0 0% 10%);
   --ring: oklch(0.55 0.22 25); /* Red ring color */
   --chart-1: oklch(0.646 0.222 41.116);
   --chart-2: oklch(0.6 0.118 184.704);
   --chart-3: oklch(0.398 0.07 227.392);
   --chart-4: oklch(0.828 0.189 84.429);
   --chart-5: oklch(0.769 0.188 70.08);

   --sidebar: hsl(0 0% 2%);
   --sidebar-foreground: oklch(0.145 0 0);
   --sidebar-primary: oklch(0.205 0 0);
   --sidebar-primary-foreground: oklch(0.985 0 0);
   --sidebar-accent: oklch(0.55 0.22 25);
   --sidebar-accent-foreground: oklch(0.205 0 0);
   --sidebar-border: oklch(0.922 0 0);
   --sidebar-ring: oklch(0.708 0 0);

   /* Custom Astral Studios colors */
   --astral-red: 0 84% 60%;
   --astral-red-light: 0 84% 70%;
   --astral-red-dark: 0 84% 45%;
   --astral-black: 0 0% 2%;
   --astral-grey: 0 0% 8%;
   --astral-grey-light: 0 0% 15%;

   /* Gradients */
   --gradient-hero: linear-gradient(135deg, hsl(0 84% 60% / 0.1), hsl(0 0% 2%));
   --gradient-accent: linear-gradient(90deg, hsl(0 84% 60%), hsl(0 84% 45%));
   --gradient-overlay: linear-gradient(
      180deg,
      hsl(0 0% 2% / 0.8),
      hsl(0 0% 2% / 0.4)
   );

   /* Shadows */
   --shadow-glow: 0 0 40px hsl(0 84% 60% / 0.3);
   --shadow-card: 0 10px 30px hsl(0 0% 0% / 0.5);
   --shadow-elegant: 0 20px 60px hsl(0 0% 0% / 0.4);

   /* Animations */
   --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
   --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@layer base {
   * {
      @apply border-border outline-ring/50;
   }
   body {
      @apply bg-background text-foreground;
   }
}

/* Gradients */
@utility bg-gradient-hero {
   background-image: linear-gradient(
      135deg,
      rgba(255, 51, 51, 0.1),
      rgba(5, 5, 5, 1)
   );
}

@utility bg-gradient-accent {
   background-image: linear-gradient(
      90deg,
      rgba(255, 51, 51, 1),
      rgba(204, 41, 41, 1)
   );
}

@utility bg-gradient-overlay {
   background-image: linear-gradient(
      180deg,
      rgba(5, 5, 5, 0.8),
      rgba(5, 5, 5, 0.4)
   );
}

/* Custom scrollbar */
::-webkit-scrollbar {
   width: 5px;
}

::-webkit-scrollbar-track {
   background: #000;
}

::-webkit-scrollbar-thumb {
   background: #991b1b;
   border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
   background: #dc2626;
}

.masonry-container {
   display: flex;
   flex-direction: column;
   /* gap: 20px; */
   flex-wrap: wrap;
}

.item:nth-child(1),
.item:nth-child(5),
.item:nth-child(7) {
   width: 32%;
   height: 40%;
}

.item:nth-child(2),
.item:nth-child(8) {
   width: 32%;
   height: 30%;
}

.item:nth-child(3),
.item:nth-child(6),
.item:nth-child(9) {
   width: 32%;
   height: 25%;
   flex-grow: 1;
}

.item:nth-child(4) {
   width: 32%;
   height: 25%;
}
