"use client";

import { PaginationOptions, UpdateAlbumInput } from "@/lib/models";
import {
   createAlbum,
   deleteAlbum,
   getAlbumById,
   getAlbums,
   getPublicAlbums,
   updateAlbum,
} from "@/lib/services/album-service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { invalidationKeys, queryKeys } from "./query-keys";

/**
 * Hook to fetch albums with pagination
 */
export function useAlbums(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.albums.list(options),
      queryFn: () => getAlbums(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch public albums only
 */
export function usePublicAlbums(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.albums.public(options),
      queryFn: () => getPublicAlbums(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch a single album by ID
 */
export function useAlbum(id: string) {
   return useQuery({
      queryKey: queryKeys.albums.detail(id),
      queryFn: () => getAlbumById(id),
      enabled: !!id,
      staleTime: 10 * 60 * 1000, // 10 minutes
   });
}

/**
 * Hook to create a new album
 */
export function useCreateAlbum() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: createAlbum,
      onSuccess: () => {
         // Invalidate albums list
         queryClient.invalidateQueries({
            queryKey: invalidationKeys.albumsList(),
         });
         toast.success("Album created successfully");
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to create album"
         );
      },
   });
}

/**
 * Hook to update an album
 */
export function useUpdateAlbum() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ id, input }: { id: string; input: UpdateAlbumInput }) =>
         updateAlbum(id, input),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific album in cache
            queryClient.setQueryData(
               queryKeys.albums.detail(variables.id),
               data
            );

            // Invalidate lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.albumsList(),
            });

            toast.success("Album updated successfully");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to update album"
         );
      },
   });
}

/**
 * Hook to delete an album
 */
export function useDeleteAlbum() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deleteAlbum,
      onSuccess: (success) => {
         if (success) {
            // Invalidate all album queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allAlbums(),
            });
            toast.success("Album deleted successfully");
         } else {
            toast.error("Failed to delete album");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to delete album"
         );
      },
   });
}
