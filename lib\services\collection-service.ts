"use server";

import {
   Collection,
   CollectionWithStats,
   CreateCollectionInput,
   DEFAULT_PAGINATION,
   PaginatedResponse,
   PaginationOptions,
   UpdateCollectionInput,
   createCollectionMetadata,
   createPaginationMetadata,
   validateCollectionInput,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get all collections with pagination
 */
export async function getCollections(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<CollectionWithStats>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const collectionCollection = await getCollection<Collection>(
         "collections"
      );

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Get collections with image counts
      const pipeline = [
         {
            $lookup: {
               from: "images",
               let: { collectionId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $in: [
                              "$$collectionId",
                              { $ifNull: ["$collectionIds", []] },
                           ],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0, // Remove the images array, keep only the count
            },
         },
         { $sort: sort },
         { $skip: skip },
         { $limit: limit },
      ];

      const [collections, total] = await Promise.all([
         collectionCollection.aggregate(pipeline).toArray(),
         collectionCollection.countDocuments(),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      // Convert ObjectIds to strings for client component serialization
      const serializedCollections = collections.map((collection) => ({
         ...collection,
         _id: collection._id?.toString(),
      }));

      return {
         data: serializedCollections as CollectionWithStats[],
         pagination,
      };
   } catch (error) {
      console.error("Error fetching collections:", error);
      throw new Error("Failed to fetch collections");
   }
}

/**
 * Get collection by ID
 */
export async function getCollectionById(
   id: string
): Promise<CollectionWithStats | null> {
   try {
      const collectionCollection = await getCollection<Collection>(
         "collections"
      );

      const pipeline = [
         { $match: { _id: new ObjectId(id) } },
         {
            $lookup: {
               from: "images",
               let: { collectionId: { $toString: "$_id" } },
               pipeline: [
                  {
                     $match: {
                        $expr: {
                           $in: [
                              "$$collectionId",
                              { $ifNull: ["$collectionIds", []] },
                           ],
                        },
                     },
                  },
               ],
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
      ];

      const result = await collectionCollection.aggregate(pipeline).toArray();
      const collection = result[0] as CollectionWithStats;

      if (!collection) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...collection,
         _id: collection._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching collection by ID:", error);
      throw new Error("Failed to fetch collection");
   }
}

/**
 * Create a new collection
 */
export async function createCollection(
   input: CreateCollectionInput
): Promise<Collection> {
   try {
      // Validate input
      const errors = validateCollectionInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      const collection = await getCollection<Collection>("collections");
      const collectionData = createCollectionMetadata(input);

      const result = await collection.insertOne(collectionData);

      if (!result.insertedId) {
         throw new Error("Failed to create collection");
      }

      const createdCollection = await collection.findOne({
         _id: result.insertedId,
      });

      if (!createdCollection) {
         throw new Error("Failed to retrieve created collection");
      }

      // Convert ObjectId to string for client component serialization
      return {
         ...createdCollection,
         _id: createdCollection._id?.toString(),
      };
   } catch (error) {
      console.error("Error creating collection:", error);
      throw error;
   }
}

/**
 * Update a collection
 */
export async function updateCollection(
   id: string,
   input: UpdateCollectionInput
): Promise<Collection | null> {
   try {
      const collection = await getCollection<Collection>("collections");

      const updateData = {
         ...input,
         updatedAt: new Date(),
      };

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating collection:", error);
      throw new Error("Failed to update collection");
   }
}

/**
 * Delete a collection
 */
export async function deleteCollection(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Collection>("collections");

      const result = await collection.deleteOne({ _id: new ObjectId(id) });

      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error deleting collection:", error);
      throw new Error("Failed to delete collection");
   }
}
