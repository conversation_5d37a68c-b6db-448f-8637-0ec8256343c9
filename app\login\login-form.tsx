"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   checkFirstTimeSetup,
   loginAction,
   performFirstTimeSetup,
} from "@/lib/actions/auth-actions";
import { LockClosedIcon } from "@heroicons/react/24/solid";
import { Eye, EyeOff, Shield } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function LoginForm() {
   const [password, setPassword] = useState("");
   const [confirmPassword, setConfirmPassword] = useState("");
   const [showPassword, setShowPassword] = useState(false);
   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
   const [isLoading, setIsLoading] = useState(false);
   const [needsSetup, setNeedsSetup] = useState(false);
   const [checkingSetup, setCheckingSetup] = useState(true);

   const router = useRouter();
   const searchParams = useSearchParams();
   const redirectTo = searchParams.get("redirect") || "/admin";

   // Check if first-time setup is needed
   useEffect(() => {
      async function checkSetup() {
         try {
            const result = await checkFirstTimeSetup();
            if (result.success && result.data) {
               setNeedsSetup(result.data.needsSetup);
            }
         } catch (error) {
            console.error("Error checking setup:", error);
            toast.error("Failed to check setup status");
         } finally {
            setCheckingSetup(false);
         }
      }

      checkSetup();
   }, []);

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setIsLoading(true);

      try {
         if (needsSetup) {
            if (password !== confirmPassword) {
               toast.error("Passwords do not match");
               return;
            }

            const result = await performFirstTimeSetup({ password });
            if (result.success) {
               toast.success("Setup completed successfully");
               router.push(redirectTo);
            } else {
               toast.error(result.error || "Setup failed");
            }
         } else {
            const result = await loginAction({ password });
            if (result.success) {
               toast.success("Login successful");
               router.push(redirectTo);
            } else {
               toast.error(result.error || "Login failed");
            }
         }
      } catch (error) {
         console.error("Error during login/setup:", error);
         toast.error("An unexpected error occurred");
      } finally {
         setIsLoading(false);
      }
   };

   if (checkingSetup) {
      return <div>Loading...</div>;
   }

   return (
      <div className="flex min-h-screen items-center justify-center">
         <div className="mx-auto w-full max-w-md space-y-6 rounded-lg bg-card p-6 shadow-lg">
            <div className="flex flex-col items-center space-y-2 text-center">
               <div className="relative h-20 w-20">
                  <Image
                     src="/astral-logo.svg"
                     alt="Astral Logo"
                     fill
                     className="object-contain"
                     priority
                  />
               </div>
               <div className="space-y-1">
                  <h1 className="text-2xl font-bold tracking-tighter text-foreground sm:text-3xl">
                     {needsSetup ? "Welcome to Astral" : "Welcome Back"}
                  </h1>
                  <p className="text-sm text-muted-foreground">
                     {needsSetup ? (
                        <span className="flex items-center justify-center gap-1">
                           <Shield className="h-4 w-4" />
                           Set up your admin password
                        </span>
                     ) : (
                        "Enter your password to access the admin panel"
                     )}
                  </p>
               </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
               <div className="space-y-2">
                  <Label htmlFor="password" className="text-foreground">
                     <span className="flex items-center gap-1">
                        <LockClosedIcon className="h-4 w-4" />
                        Password
                     </span>
                  </Label>
                  <div className="relative">
                     <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        className="pr-10"
                     />
                     <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
                     >
                        {showPassword ? (
                           <EyeOff className="h-4 w-4" />
                        ) : (
                           <Eye className="h-4 w-4" />
                        )}
                     </button>
                  </div>
               </div>

               {needsSetup && (
                  <div className="space-y-2">
                     <Label
                        htmlFor="confirm-password"
                        className="text-foreground"
                     >
                        <span className="flex items-center gap-1">
                           <LockClosedIcon className="h-4 w-4" />
                           Confirm Password
                        </span>
                     </Label>
                     <div className="relative">
                        <Input
                           id="confirm-password"
                           type={showConfirmPassword ? "text" : "password"}
                           value={confirmPassword}
                           onChange={(e) => setConfirmPassword(e.target.value)}
                           required
                           className="pr-10"
                        />
                        <button
                           type="button"
                           onClick={() =>
                              setShowConfirmPassword(!showConfirmPassword)
                           }
                           className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
                        >
                           {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4" />
                           ) : (
                              <Eye className="h-4 w-4" />
                           )}
                        </button>
                     </div>
                  </div>
               )}

               <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading
                     ? needsSetup
                        ? "Setting up..."
                        : "Logging in..."
                     : needsSetup
                     ? "Complete Setup"
                     : "Login"}
               </Button>
            </form>
         </div>
      </div>
   );
}
