import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { <PERSON>, Key, Settings, Shield, User } from "lucide-react";

export default function AccountPage() {
   const accountSections = [
      {
         title: "Profile Settings",
         description: "Manage your profile information",
         icon: User,
      },
      {
         title: "Security",
         description: "Password and security settings",
         icon: Shield,
      },
      {
         title: "API Keys",
         description: "Manage API access tokens",
         icon: Key,
      },
      {
         title: "Notifications",
         description: "Configure notification preferences",
         icon: Bell,
      },
   ];

   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center space-x-4">
            <div>
               <h1 className="text-3xl font-bold text-foreground">
                  Account Settings
               </h1>
               <p className="text-muted-foreground">
                  Manage your account preferences and security settings
               </p>
            </div>
         </div>

         {/* Account Overview */}
         <Card className="border-border/50">
            <CardHeader>
               <CardTitle className="text-foreground">
                  Account Overview
               </CardTitle>
               <CardDescription>
                  Your current account information
               </CardDescription>
            </CardHeader>
            <CardContent>
               <div className="flex items-center space-x-4 p-4 bg-accent/50 rounded-lg">
                  <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center">
                     <User className="w-8 h-8 text-white" />
                  </div>
                  <div>
                     <h3 className="text-lg font-semibold text-foreground">
                        Admin User
                     </h3>
                     <p className="text-muted-foreground">
                        <EMAIL>
                     </p>
                     <p className="text-sm text-muted-foreground">
                        Administrator
                     </p>
                  </div>
               </div>
            </CardContent>
         </Card>

         {/* Settings Sections */}
         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {accountSections.map((section) => {
               const Icon = section.icon;
               return (
                  <Card key={section.title} className="border-border/50">
                     <CardHeader>
                        <div className="flex items-center space-x-3">
                           <div className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                              <Icon className="w-5 h-5 text-muted-foreground" />
                           </div>
                           <div>
                              <CardTitle className="text-foreground">
                                 {section.title}
                              </CardTitle>
                              <CardDescription>
                                 {section.description}
                              </CardDescription>
                           </div>
                        </div>
                     </CardHeader>
                     <CardContent>
                        <div className="flex items-center justify-center h-24 text-muted-foreground">
                           <div className="text-center">
                              <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                              <p className="text-sm">Coming soon</p>
                           </div>
                        </div>
                     </CardContent>
                  </Card>
               );
            })}
         </div>
      </div>
   );
}
